server:
  port: 8080
spring:
  mvc:
    favicon:
      enabled: false
  application:
    name: external-server
  profiles:
    active: ${BUILD_ENV:test}
  cloud:
    gateway:
      discovery:
        locator:
          #表明gateway开启服务注册和发现的功能，并且spring cloud gateway自动根据服务发现为每一个服务创建了一个router，# 这个router将以服务名开头的请求路径转发到对应的服务
          enabled: false
          #将请求路径上的服务名配置为小写（因为服务注册的时候，向注册中心注册时将服务名转成大写的了,比如以/service-hi/*的请求路径被路由转发到服务名为service-hi的服务上
          lowerCaseServiceId: true
    nacos:
      config:
        username: ${NACOS-USERNAME:}
        password: ${NACOS-PASSWORD:}
        namespace: openplatform
        server-addr: ${NACOS_ADDR:10.72.5.198:8848}
        group: ${spring.profiles.active}
        shared-configs[0]:
          data-id: external-${spring.profiles.active}.yml # 配置文件名-Data Id
          group: ${spring.profiles.active}   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
      discovery:
        username: ${NACOS-USERNAME:}
        password: ${NACOS-PASSWORD:}
        namespace: openplatform
        server-addr: ${NACOS_ADDR:10.72.5.198:8848}
        group: op-${spring.profiles.active}
  main:
    allow-bean-definition-overriding: true
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: LEGACYHTML5
    prefix: classpath:/templates/
    suffix: .html

#mybatis plus 设置
mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.com.hyundai.open.fragment.spring.server.**.entity
  mapper-locations: classpath:mapper/*.xml
