package cn.com.hyundai.open.external.server;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ImportResource;


/**
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {})
@ImportResource("classpath:beanRefContext.xml")
@MapperScan(basePackages = "cn.com.hyundai.open.external.server.mybatis.mapper")
public class ExternalApplication {

    public static void main(String[] args) {
        SpringApplication.run(ExternalApplication.class, args);
        System.out.println("程序启动成功----");
    }


}
