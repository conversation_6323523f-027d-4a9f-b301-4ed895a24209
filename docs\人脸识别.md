# 【实名平台】【交互式人脸识别服务】接口规范说明书

## 文件信息
- **文件状态**：草稿
- **文件标识**：CBIT -Project- RD-SM
- **当前版本**：1.1
- **编制部门**：科技研发一部
- **作者**：统一实名认证平台项目组
- **完成日期**：2024-05-21

## 关于本文档
- **主题**：描述统一实名认证平台-交互式人脸识别接口交互方案
- **说明**：本文档用于定义统一实名认证平台-交互式人脸识别接口交互方案与请求方系统交互方案^[1]^
- **适用对象**：统一实名认证平台-交互式人脸识别开发维护人员、请求方系统项目组^[2]^

## 修订历史
- **版本 1.0.0**：全部章节，创建（C），日期：2023-08-28
- **版本 1.1.0**：
  - 4.1.1.3章节，修改（U），日期：2024-05-21，说明：对回调地址进行补充说明^[3]^
  - 6.1.3章节，修改（U），日期：2024-05-21，说明：对人脸识别结果代码进行调整^[4]^
  - 6.1.7章节，修改（U），日期：2024-05-22，说明：删除渠道代码^[5]^

## 文档介绍
### 文档目的
描述交互式人脸识别与请求方系统间的数据交互方案

### 文档范围
包括交互式人脸识别接口的交互流程和数据格式等^[6]^

### 读者对象
统一实名认证平台项目组全体人员和请求方项目组全体人员，包含开发、测试、运维、项目经理^[7]^

### 术语与缩写解释
- **唯一标识码**：由统一实名认证平台生成，代表本次认证交易的唯一交易码
- **查验码**：由统一实名认证平台生成，代表本次认证交易查验人的唯一交易码^[8]^

## 系统说明
- **通讯协议**：http协议
- **通讯方式**：post
- **报文格式**：JSON
- **字符编码**：UTF-8
- **访问形式**：通过“http://ip:端口/项目名/服务名”的形式访问（服务名：接口的服务名称）^[9]^
- **密码采用AES加密**：salt使用统一实名认证平台分配的salt，其他加密属性如下：
    加密模式：ECB
    填充：PKCS5Padding
    数据块：128位
    输出模式：Hex
    编码：UTF8

### 系统交互流程
1. 调用“活检链接生成接口”获取活检链接，并指定活检结束后的回调页面^[10]^
2. 用户在活检页面操作结束后，调用“识别结果获取接口”，获取活检及识别结果^[11]^

## 接口分类

### 活检链接生成接口
#### 请求信息
- **类名**：ReqDto
- **对象属性**：
  - **reqHeadDto**：请求头信息；参见对象
  - **reqAuthDto**：请求体信息；参见对象

##### 请求头信息
- **类名**：ReqHeadDto
- **对象属性**：
  - **cipherToken**：String，32，Y，密钥，管理系统创建生成^[12]^
  - **authCode**：String，32，Y，认证代码^[13]^
  - **cipherPass**：String，32，Y，AES加密的密码^[14]^

##### 请求体信息
- **类名**：ReqAuthDto
- **对象属性**：
  - **reqAuthInfoDto**：服务参数；参见对象
  - **channelCode**：String，5，Y，渠道代码；参见代码^[15]^
  - **companyCode**：String，11，Y，机构代码；参见代码^[16]^
  - **supPrefCode**：String，6，N，监管辖区代码；参见代码^[17]^
  - **busLinkCode**：String，3，N，业务类型代码；参见代码^[18]^
  - **classCode**：String，5，N，业务种类代码；参见代码^[19]^
  - **busNo**：String，50，N，业务单号^[20]^
  - **twoCompany**：String，50，Y，二级分支机构代码^[21]^
  - **threeCompany**：String，50，Y，三级分支机构代码^[22]^

##### 服务参数
- **类名**：ReqAuthInfoDto
- **对象属性**：
  - **serviceCode**：String，5，Y，此项查验服务的服务代码；参见代码^[23]^
  - **validDate**：String，10，N，有效止期^[24]^
  - **name**：String，200，Y，姓名^[25]^
  - **certType**：String，4，Y，证件类型；参见代码^[26]^
  - **certCode**：String，50，Y，证件号码^[27]^
  - **url**：String，500，Y，回调地址^[28]^

#### 返回信息
- **类名**：ResDto
- **对象属性**：
  - **resHeadDto**：返回头信息；参见对象
  - **resAuthDto**：返回体信息；参见对象

##### 返回头信息
- **类名**：ResHeadDto
- **对象属性**：
  - **resCode**：String，6，Y，接口响应码；参见代码^[29]^
  - **resMessage**：String，30，Y，响应码描述^[30]^

##### 返回体信息
- **类名**：ResAuthDto
- **对象属性**：
  - **resBiInfo**：活检链接生成结果；参见对象
  - **transactionId**：String，32，Y，唯一标识码^[31]^
  - **busLinkCode**：String，3，N，业务类型代码^[32]^
  - **classCode**：String，5，N，业务种类代码^[33]^
  - **busNo**：String，50，N，业务单号^[34]^

##### 活检链接生成结果
- **类名**：ResBiInfo
- **对象属性**：
  - **serviceCode**：String，5，Y，此项查验服务使用的服务代码；参见代码^[35]^
  - **authResult**：String，5，Y，链接生成结果代码；参见代码^[36]^
  - **url**：String，500，N，活检链接^[37]^

### 识别结果获取接口
#### 请求信息
- **类名**：ReqDto
- **对象属性**：
  - **reqHeadDto**：请求头信息；参见对象
  - **reqAuthDto**：请求体信息；参见对象

##### 请求头信息
- **类名**：ReqHeadDto
- **对象属性**：
  - **cipherToken**：String，32，Y，密钥^[38]^
  - **authCode**：String，32，Y，认证代码^[39]^
  - **cipherPass**：String，32，Y，AES加密的密码^[40]^

##### 请求体信息
- **类名**：ReqAuthDto
- **对象属性**：
  - **reqAuthInfoDto**：服务参数；参见对象
  - **channelCode**：String，5，Y，渠道代码；参见代码^[41]^
  - **companyCode**：String，11，Y，机构代码；参见代码^[42]^
  - **supPrefCode**：String，6，N，监管辖区代码；参见代码^[43]^

##### 服务参数
- **类名**：ReqAuthInfoDto
- **对象属性**：
  - **serviceCode**：String，5，Y，此项查验服务的服务代码；参见代码^[44]^
  - **transactionId**：String，32，Y，唯一标识码^[45]^

#### 返回信息
- **类名**：ResDto
- **对象属性**：
  - **resHeadDto**：返回头信息；参见对象
  - **resAuthDto**：返回体信息；参见对象

##### 返回头信息
- **类名**：ResHeadDto
- **对象属性**：
  - **resCode**：String，6，Y，接口响应码；参见代码^[46]^
  - **resMessage**：String，30，Y，响应码描述^[47]^

##### 返回体信息
- **类名**：ResAuthDto
- **对象属性**：
  - **resReInfo**：识别结果；参见对象
  - **tId**：String，32，N，人脸认证唯一标识码^[48]^
  - **checkCode**：String，32，N，查验码^[49]^
  - **authenticationId**：String，32，N，实名ID^[50]^

##### 识别结果
- **类名**：ResReInfo
- **对象属性**：
  - **authResult**：String，5，Y，人脸识别结果代码；参见代码^[51]^
  - **score**：BigDecimal，N，相似度^[52]^
  - **image**：String，N，人脸图片^[53]^
  - **updateDate**：String，N，更新日期^[54]^
  - **authTime**：String，N，认证时间^[55]^

## 其它要说明的事项
只有密钥、认证代码、密码密文正确时，接口调用才能成功^[56]^

## 附录

### 代码定义
#### 接口响应码代码
- **200**：成功（业务请求成功）
- **201**：调用xxx超时
- **203**：xx机构的xxx服务瞬时请求数过多，请稍后再试
- **999**：其他未知系统异常
- **300**：请求参数有误。（具体描述以实际为准）
- **301**：无访问权限
- **302**：XXXX（机构名称）下XXXX（服务名称）已超当日调用上限，请明天再进行访问
- **303**：XXXX的值不在有效范围内
- **304**：认证服务未启用
- **307**：无可使用的信源代码

#### 链接生成结果代码
- **10701**：活检链接生成成功
- **20701**：活检链接生成失败

#### 人脸识别结果代码
- **10401**：活体检测成功，姓名与身份证号匹配成功且人脸验证通过，系统判断为同一人
- **20401**：活体检测成功，姓名与身份证号匹配成功且人脸验证未通过，系统判断为不同人
- **20402**：活体检测成功，姓名与身份证号匹配一致，库中无照片
- **20403**：活体检测成功，姓名与身份证号匹配一致，照片质量不佳
- **30401**：活体检测成功，姓名与身份证号不匹配
- **30402**：活体检测成功，库中无身份证号
- **40401**：活体检测链接获取失败
- **40402**：活体检测链接不存在或者已过期
- **40403**：活体检测未完成
- **40404**：浏览器环境不支持活体检测
- **40405**：用户拒绝打开摄像头
- **40406**：未能检测到人脸或持续出现多人脸
- **40407**：人脸移出视线或出现多人脸
- **40408**：检测到有可能摄像头被劫持
- **40409**：用户做出不匹配指令的错误动作
- **40410**：未在等待时间内集到用户做出的正确动作
- **40411**：未能采集到质量合格的活体人脸照片
- **40412**：未通过前端活体检测算法
- **40413**：未通过后端活体检测算法
- **40414**：活体检测其他错误
- **40415**：活体检测账号密码不匹配
- **40416**：活体检测请求参数有误
- **40417**：活体检测查询超时
- **40418**：活体检测服务异常
- **90001**：账号密码不匹配
- **90002**：请求参数有误
- **90003**：查询超时
- **90004**：服务异常
- **90005**：其他错误
- **40701**：唯一码已失效

#### 监管辖区代码
- **000000**：全局
- **110000**：北京
- **120000**：天津
- ...（其他代码略）

#### 机构代码
取值范围：引用《中国银保信业务代码集_V4.2_20200109》“金融许可证机构代码”，编号CD000589；“保险机构代码”CD000012。^[57]^

#### 业务种类代码
取值范围: 引用《中国银保信业务代码集_V4.2_20200109》“险类代码”，编号CD000039。^[58]^
- **01000**：机动车辆保险
- **01100**：机动车商业保险（非强制保险）
- ...（其他代码略）

#### 业务类型代码
取值范围：部分引用《中国银保信业务代码集_V4.2_20200109》“业务类型代码”，编号CD000089，其余差异部分需新增。^[59]^
- **101**：投保申请
- **102**：投保确认
- ...（其他代码略）

#### 证件类型代码
- **111**：居民身份证^[60]^

#### 服务代码
- **102**：交互式人脸识别